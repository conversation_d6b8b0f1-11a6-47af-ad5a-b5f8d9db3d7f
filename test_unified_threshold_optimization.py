#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一决策阈值优化目标
验证网格搜索和Optuna都使用基于盈利能力的优化目标
"""

import numpy as np
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
import config
from src.core.prediction import (
    optimize_meta_decision_thresholds,
    calculate_simulated_profit_meta,
    _calculate_optimization_objective
)

def generate_test_data(n_samples=1000):
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成三分类标签 (0=下跌, 1=上涨, 2=中性)
    y_true = np.random.choice([0, 1, 2], size=n_samples, p=[0.3, 0.3, 0.4])
    
    # 生成概率预测 (3个类别)
    y_proba = np.random.dirichlet([1, 1, 1], size=n_samples)
    
    # 添加一些信号，让某些预测更有信心
    for i in range(n_samples):
        if y_true[i] == 0:  # 真实下跌
            y_proba[i] = [0.6, 0.2, 0.2]  # 给下跌更高概率
        elif y_true[i] == 1:  # 真实上涨
            y_proba[i] = [0.2, 0.6, 0.2]  # 给上涨更高概率
        # 中性保持随机
    
    return y_true, y_proba

def test_optimization_consistency():
    """测试优化目标的一致性"""
    print("🧪 测试统一决策阈值优化目标")
    print("=" * 60)
    
    # 生成测试数据
    y_true, y_proba = generate_test_data(500)
    print(f"✅ 生成测试数据: {len(y_true)} 样本")
    print(f"   类别分布: {np.bincount(y_true)}")
    
    # 测试calculate_simulated_profit_meta函数
    print("\n📊 测试 calculate_simulated_profit_meta 函数")
    test_result = calculate_simulated_profit_meta(
        y_true, y_proba,
        threshold_up=0.4,
        threshold_down=0.4,
        confidence_gap_up=0.1,
        confidence_gap_down=0.1,
        verbose=True
    )
    
    print(f"   期望收益/交易: {test_result['expected_profit_per_trade']:.4f}")
    print(f"   风险调整收益: {test_result['risk_adjusted_return']:.4f}")
    print(f"   总交易次数: {test_result['total_trades']}")
    print(f"   胜率: {test_result['win_rate']:.3f}")
    
    # 测试优化目标计算
    print("\n🎯 测试优化目标计算")
    
    # 测试不同的优化策略
    strategies = [
        'risk_adjusted_return',
        'composite_score', 
        'expected_profit',
        'balanced_profit_frequency'
    ]
    
    for strategy in strategies:
        # 临时设置配置
        original_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'risk_adjusted_return')
        config.META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY = strategy
        
        objective_score = _calculate_optimization_objective(test_result, len(y_true))
        print(f"   策略 '{strategy}': {objective_score:.4f}")
        
        # 恢复原配置
        config.META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY = original_strategy
    
    # 测试网格搜索优化 (小范围快速测试)
    print("\n🔍 测试网格搜索优化 (快速版)")
    
    # 临时设置小范围搜索以加快测试
    original_threshold_min = getattr(config, 'META_MODEL_THRESHOLD_MIN', 0.1)
    original_threshold_max = getattr(config, 'META_MODEL_THRESHOLD_MAX', 0.9)
    original_gap_min = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MIN', 0.0)
    original_gap_max = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MAX', 0.5)
    
    # 设置小范围用于快速测试
    config.META_MODEL_THRESHOLD_MIN = 0.3
    config.META_MODEL_THRESHOLD_MAX = 0.5
    config.META_MODEL_CONFIDENCE_GAP_MIN = 0.0
    config.META_MODEL_CONFIDENCE_GAP_MAX = 0.2
    config.META_MODEL_MIN_TRADES_CONSTRAINT = 3  # 降低约束以便测试
    config.META_MODEL_MIN_WIN_RATE_CONSTRAINT = 0.2
    
    try:
        grid_result = optimize_meta_decision_thresholds(
            y_true, y_proba,
            optimization_method='grid_search',
            verbose=True
        )
        
        print(f"   网格搜索最优结果:")
        print(f"     UP阈值: {grid_result['threshold_up']:.3f}")
        print(f"     DOWN阈值: {grid_result['threshold_down']:.3f}")
        print(f"     期望收益/交易: {grid_result['expected_profit_per_trade']:.4f}")
        print(f"     总交易次数: {grid_result['total_trades']}")
        print(f"     胜率: {grid_result['win_rate']:.3f}")
        
    except Exception as e:
        print(f"   ⚠️ 网格搜索测试失败: {e}")
    
    # 测试Optuna优化 (少量试验)
    print("\n🎯 测试Optuna优化 (快速版)")
    
    try:
        optuna_result = optimize_meta_decision_thresholds(
            y_true, y_proba,
            optimization_method='optuna',
            n_trials=10,  # 少量试验用于快速测试
            verbose=True
        )
        
        print(f"   Optuna最优结果:")
        print(f"     UP阈值: {optuna_result['threshold_up']:.3f}")
        print(f"     DOWN阈值: {optuna_result['threshold_down']:.3f}")
        print(f"     期望收益/交易: {optuna_result['expected_profit_per_trade']:.4f}")
        print(f"     总交易次数: {optuna_result['total_trades']}")
        print(f"     胜率: {optuna_result['win_rate']:.3f}")
        
    except Exception as e:
        print(f"   ⚠️ Optuna测试失败: {e}")
    
    # 恢复原配置
    config.META_MODEL_THRESHOLD_MIN = original_threshold_min
    config.META_MODEL_THRESHOLD_MAX = original_threshold_max
    config.META_MODEL_CONFIDENCE_GAP_MIN = original_gap_min
    config.META_MODEL_CONFIDENCE_GAP_MAX = original_gap_max
    
    print("\n✅ 统一决策阈值优化测试完成!")
    print("🎯 核心验证点:")
    print("   1. calculate_simulated_profit_meta 函数正常工作")
    print("   2. _calculate_optimization_objective 支持多种策略")
    print("   3. 网格搜索和Optuna都使用相同的优化目标")
    print("   4. 优化目标直接基于模拟交易的盈利能力")

if __name__ == "__main__":
    test_optimization_consistency()
